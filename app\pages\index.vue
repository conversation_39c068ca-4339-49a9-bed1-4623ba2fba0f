<script setup lang="ts">
const { data: page } = await useAsyncData('index', () => {
  return queryCollection('index').first()
})
if (!page.value) {
  throw createError({
    statusCode: 404,
    statusMessage: 'Page not found',
    fatal: true
  })
}

useSeoMeta({
  title: page.value?.seo.title || page.value?.title,
  ogTitle: page.value?.seo.title || page.value?.title,
  description:
    page.value?.seo.description || page.value?.description,
  ogDescription:
    page.value?.seo.description || page.value?.description
})
</script>

<template>
  <div v-if="page">
    <LandingHero :page />
    <section class="py-2 sm:py-4 lg:py-8 overflow-hidden">
      <div class="flex flex-col lg:gap-8">
        <LandingAbout :page />
        <LandingWorkExperience :page />
      </div>
    </section>
    <LandingBlog :page />
    <LandingTestimonials :page />
    <LandingFAQ :page />
  </div>
</template>
