<script setup lang="ts">
interface Props {
  title?: string
  description?: string
  headline?: string
  orientation?: 'horizontal' | 'vertical'
  reverse?: boolean
  ui?: Record<string, any>
  class?: any
}

const props = withDefaults(defineProps<Props>(), {
  orientation: 'vertical',
  reverse: false,
  ui: () => ({})
})

defineSlots<{
  default?: any
  leading?: any
  headline?: any
  title?: any
  description?: any
  body?: any
  footer?: any
  links?: any
}>()

const containerClasses = computed(() => {
  const base =
    'relative isolate flex flex-col py-12 sm:py-14 lg:py-18 gap-8 sm:gap-16'
  const orientationClasses = {
    horizontal: 'lg:grid lg:grid-cols-2 lg:items-center',
    vertical: ''
  }
  const reverseClass = props.reverse ? 'lg:order-last' : ''

  return [
    base,
    orientationClasses[props.orientation],
    reverseClass,
    props.ui?.container
  ]
    .filter(Boolean)
    .join(' ')
})

const titleClasses = computed(() => {
  const base =
    'text-3xl sm:text-4xl lg:text-5xl text-pretty tracking-tight font-bold text-highlighted'
  const orientationClasses = {
    horizontal: '',
    vertical: 'text-center'
  }

  return [
    base,
    orientationClasses[props.orientation],
    props.ui?.title
  ]
    .filter(Boolean)
    .join(' ')
})

const descriptionClasses = computed(() => {
  const base = 'text-base sm:text-lg text-muted'
  const orientationClasses = {
    horizontal: 'text-pretty',
    vertical: 'text-center text-balance'
  }

  return [
    base,
    orientationClasses[props.orientation],
    props.ui?.description
  ]
    .filter(Boolean)
    .join(' ')
})

const headlineClasses = computed(() => {
  const base = 'mb-3'
  const orientationClasses = {
    horizontal: '',
    vertical: 'justify-center'
  }

  return [
    base,
    orientationClasses[props.orientation],
    props.ui?.headline
  ]
    .filter(Boolean)
    .join(' ')
})

const linksClasses = computed(() => {
  const base = 'flex flex-wrap gap-x-6 gap-y-3'
  const orientationClasses = {
    horizontal: '',
    vertical: 'justify-center'
  }

  return [
    base,
    orientationClasses[props.orientation],
    props.ui?.links
  ]
    .filter(Boolean)
    .join(' ')
})
</script>

<template>
  <section :class="[containerClasses, props.class]">
    <div
      v-if="
        $slots.leading ||
        $slots.headline ||
        title ||
        description
      "
      class="wrapper"
      :class="props.ui?.wrapper"
    >
      <div class="header" :class="props.ui?.header">
        <div
          v-if="$slots.leading"
          class="flex items-center mb-6"
          :class="[
            props.orientation === 'vertical'
              ? 'justify-center'
              : '',
            props.ui?.leading
          ]"
        >
          <slot name="leading" />
        </div>

        <div
          v-if="$slots.headline || headline"
          :class="headlineClasses"
        >
          <slot name="headline">
            <span
              v-if="headline"
              class="font-semibold text-primary flex items-center gap-1.5"
            >
              {{ headline }}
            </span>
          </slot>
        </div>

        <h2
          v-if="$slots.title || title"
          :class="titleClasses"
        >
          <slot name="title">
            {{ title }}
          </slot>
        </h2>

        <p
          v-if="$slots.description || description"
          :class="[descriptionClasses, title ? 'mt-6' : '']"
        >
          <slot name="description">
            {{ description }}
          </slot>
        </p>
      </div>
    </div>

    <div
      v-if="$slots.body || $slots.default"
      class="mt-8"
      :class="props.ui?.body"
    >
      <slot name="body">
        <slot />
      </slot>
    </div>

    <div
      v-if="$slots.footer"
      class="mt-8"
      :class="props.ui?.footer"
    >
      <slot name="footer" />
    </div>

    <div v-if="$slots.links" :class="linksClasses">
      <slot name="links" />
    </div>
  </section>
</template>
